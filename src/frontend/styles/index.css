* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

#root {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone-container {
  width: 375px;
  height: 667px;
  max-width: 420px;
  max-height: 800px;
  background: #000;
  border-radius: 25px;
  padding: 8px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  position: relative;
}



.screen {
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  background: #007AFF;
  color: white;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.back-arrow {
  font-size: 18px;
}

.group-info h3 {
  font-size: 16px;
  font-weight: 600;
}

.group-info p {
  font-size: 12px;
  opacity: 0.8;
}

.status {
  font-size: 11px;
  opacity: 0.9;
}

.status.success { color: #34C759; }
.status.error { color: #FF3B30; }

.chat-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #fff;
}

.message {
  margin-bottom: 15px;
  max-width: 80%;
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message.user {
  margin-left: auto;
}

.message.user .bubble {
  background: #007AFF;
  color: white;
  border-bottom-right-radius: 8px;
}

.message.assistant .bubble,
.message.fora .bubble,
.message.jan .bubble,
.message.lou .bubble {
  background: #E5E5EA;
  color: #000;
}

.message:not(.user) {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #007AFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 18px;
}

.avatar.fora { background: #FF3B30; }
.avatar.jan { background: #34C759; }
.avatar.lou { background: #FF9500; }

.message-content {
  flex: 1;
  max-width: calc(100% - 38px);
}

.message:not(.user) .bubble {
  border-bottom-left-radius: 8px;
}

.message.user .message-content {
  max-width: 80%;
}

.sender-name {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #8E8E93;
}

.bubble {
  padding: 12px 16px;
  border-radius: 20px;
  font-size: 16px;
  line-height: 1.4;
  word-wrap: break-word;
}

.timestamp {
  font-size: 11px;
  color: #8E8E93;
  margin-top: 4px;
  text-align: center;
}

.message.user .timestamp {
  text-align: right;
}

.user .sender-name {
  display: none;
}

.message:not(.user) .timestamp {
  text-align: left;
}

.message.system {
  background: #fff3cd;
  color: #856404;
  text-align: center;
  font-style: italic;
  max-width: 100%;
  padding: 12px 16px;
  border-radius: 20px;
  margin: 15px auto;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  max-width: 100%;
  padding: 12px 16px;
  border-radius: 20px;
  margin: 15px auto;
}

.typing-indicator {
  display: none;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 15px;
  padding-left: 12px;
}

.typing-indicator.show {
  display: flex;
  animation: fadeIn 0.3s ease-in;
}

.typing-indicator .avatar {
  margin-top: 0;
}

.typing-dots {
  background: #E5E5EA;
  border-radius: 20px;
  padding: 12px 16px;
  display: flex;
  gap: 4px;
}

.dot {
  width: 8px;
  height: 8px;
  background: #8E8E93;
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.dot:nth-child(2) { animation-delay: 0.2s; }
.dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-10px); }
}

.input-area {
  padding: 15px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e5e5ea;
  display: flex;
  align-items: center;
  gap: 10px;
}

.input-field {
  flex: 1;
  background: white;
  border: 1px solid #e5e5ea;
  border-radius: 20px;
  padding: 10px 15px;
  font-size: 16px;
  outline: none;
}

.input-field:focus {
  border-color: #007AFF;
}

.input-field:disabled {
  background: #f8f9fa;
  cursor: not-allowed;
}

.send-btn {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
}

.send-btn:disabled {
  background: #8E8E93;
  cursor: not-allowed;
}

.skills {
  margin: 15px auto;
  padding: 8px 12px;
  background: #f0f8ff;
  border-radius: 12px;
  font-size: 0.9em;
  color: #0066cc;
  text-align: center;
  max-width: 100%;
}

/* Add styles for delayed thoughts */
.message.delayed {
  opacity: 0.8;
  max-width: 70%;
  border-left: 3px solid #9c27b0;
  padding-left: 10px;
  animation: slideIn 0.5s ease-in;
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-10px); }
  to { opacity: 0.8; transform: translateX(0); }
}

.message.delayed .message-header {
  font-style: italic;
}

.message.delayed .message-header::after {
  content: " (additional thought)";
  font-size: 0.8em;
  color: #9c27b0;
}

/* Responsive design for larger screens */
@media (min-width: 480px) {
  .phone-container {
    width: min(90vw, 400px);
    height: min(85vh, 720px);
  }
}

@media (min-width: 768px) {
  .phone-container {
    width: min(70vw, 420px);
    height: min(80vh, 750px);
  }
}

@media (min-width: 1024px) {
  body {
    padding: 40px;
  }

  .phone-container {
    width: min(50vw, 420px);
    height: min(75vh, 800px);
  }
}

@media (min-width: 1440px) {
  .phone-container {
    width: min(40vw, 420px);
    height: min(70vh, 800px);
  }
}
