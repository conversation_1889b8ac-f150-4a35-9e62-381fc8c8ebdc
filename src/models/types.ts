// Core data models and types

export interface Conversation {
  id: number;
  created_at: Date;
  updated_at: Date;
  theme?: string;
  skills?: string[] | string;
}

export interface Message {
  id: number;
  character: string;
  text: string;
  conversation_id: number;
  created_at: Date;
  updated_at: Date;
}

export interface ChatMessage {
  character: string;
  text: string;
  delay: number;
}

export interface DelayedMessage {
  id?: number;
  character: string;
  text: string;
  delay?: number;
  created_at?: Date;
}

export interface ChatResponse {
  reply: ChatMessage[];
  skills: string[];
  theme: string;
}

export interface UserRequest {
  text: string;
  userId?: string;
  sessionId?: string;
}

export interface ChatResult {
  response: ChatResponse;
  conversationId: number;
  messageId: number;
}

// Interface for different messaging platforms
export interface MessageInterface {
  sendMessage(message: string): Promise<void>;
  receiveMessage(): Promise<string>;
  formatResponse(response: ChatResponse): string;
}

// Configuration types
export interface DatabaseConfig {
  url: string;
  client: string;
}

export interface LLMConfig {
  apiKey: string;
  model: string;
  maxTokens: number;
}

export interface AppConfig {
  database: DatabaseConfig;
  llm: LLMConfig;
  port: number;
  environment: 'development' | 'production' | 'test';
}
