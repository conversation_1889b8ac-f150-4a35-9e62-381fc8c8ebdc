import { DBOS } from '@dbos-inc/dbos-sdk';
import { Conversation, Message, DelayedMessage } from '../models/types';

export class ConversationService {
  @DBOS.transaction()
  static async createConversation(): Promise<Conversation> {
    const [conversation] = await DBOS.knexClient<Conversation>('forachat.conversations')
      .insert({})
      .returning('*');
    return conversation;
  }

  @DBOS.transaction()
  static async addMessage(
    character: string, 
    text: string, 
    conversationId: number
  ): Promise<Message> {
    const [message] = await DBOS.knexClient<Message>('forachat.messages')
      .insert({
        character,
        text,
        conversation_id: conversationId
      })
      .returning('*');
    return message;
  }

  @DBOS.transaction()
  static async getConversationMessages(conversationId: number): Promise<Message[]> {
    return DBOS.knexClient<Message>('forachat.messages')
      .where('conversation_id', conversationId)
      .orderBy('created_at', 'asc');
  }

  @DBOS.transaction()
  static async getConversation(conversationId: number): Promise<Conversation | null> {
    const conversation = await DBOS.knexClient<Conversation>('forachat.conversations')
      .where('id', conversationId)
      .first();
    return conversation || null;
  }

  @DBOS.transaction()
  static async deleteConversation(conversationId: number): Promise<void> {
    // Delete messages first due to foreign key constraint
    await DBOS.knexClient('forachat.messages')
      .where('conversation_id', conversationId)
      .del();
    
    await DBOS.knexClient('forachat.conversations')
      .where('id', conversationId)
      .del();
  }

  @DBOS.transaction()
  static async getRecentConversations(limit: number = 10): Promise<Conversation[]> {
    return DBOS.knexClient<Conversation>('forachat.conversations')
      .orderBy('updated_at', 'desc')
      .limit(limit);
  }

  @DBOS.transaction()
  static async updateMessage(messageId: number, newText: string): Promise<void> {
    await DBOS.knexClient<Message>('forachat.messages')
      .where('id', messageId)
      .update({
        text: newText,
        updated_at: new Date()
      });
  }

  @DBOS.transaction()
  static async getDelayedThoughts(conversationId: number, lastMessageId?: number): Promise<DelayedMessage[]> {
    let query = DBOS.knexClient<Message>('forachat.messages')
      .where('conversation_id', conversationId)
      .where('character', '!=', 'user')
      .orderBy('id', 'asc');

    if (lastMessageId) {
      query = query.where('id', '>', lastMessageId);
    }

    const messages = await query;
    return messages.map(msg => ({
      id: msg.id,
      character: msg.character,
      text: msg.text,
      created_at: msg.created_at
    }));
  }
}
