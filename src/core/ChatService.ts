import { DBOS } from '@dbos-inc/dbos-sdk';
import { ConversationService } from './ConversationService';
import { PromptService } from './PromptService';
import { LLMService } from '../services/LLMService';
import { ChatResponse, ChatResult, UserRequest } from '../models/types';

export class ChatService {
  constructor(private llmService: LLMService) {}

  getLLMService(): LLMService {
    return this.llmService;
  }

  @DBOS.workflow()
  async processUserMessage(request: UserRequest): Promise<ChatResult> {
    // Create new conversation
    const conversation = await ConversationService.createConversation();
    
    // Add user message to conversation
    const userMessage = await ConversationService.addMessage(
      "user", 
      request.text, 
      conversation.id
    );

    // Get system prompt
    const systemPrompt = await PromptService.getSystemPrompt();
    
    // Generate LLM response
    const llmResponse = await this.llmService.generate(systemPrompt, request.text);

    // Log parsed LLM response with context
    DBOS.logger.info(`=== LLM RESPONSE (CHAT SERVICE) ===`);
    DBOS.logger.info(`Conversation: ${conversation.id}, Responder: default agent`);
    DBOS.logger.info(`Parsed Response: ${JSON.stringify(llmResponse)}`);

    // Validate response structure
    if (!this.isValidResponse(llmResponse)) {
      DBOS.logger.error(`Invalid LLM response structure: ${JSON.stringify(llmResponse)}`);
      throw new Error("Invalid response from LLM - missing or empty reply array");
    }

    // Add the first response message to the database
    // (In a real implementation, you might want to add all messages)
    const responseMessage = llmResponse.reply[0];
    const savedMessage = await ConversationService.addMessage(
      responseMessage.character, 
      responseMessage.text, 
      conversation.id
    );

    return {
      response: llmResponse,
      conversationId: conversation.id,
      messageId: savedMessage.id,
    };
  }

  @DBOS.workflow()
  async continueConversation(
    conversationId: number, 
    userMessage: string
  ): Promise<ChatResult> {
    // Verify conversation exists
    const conversation = await ConversationService.getConversation(conversationId);
    if (!conversation) {
      throw new Error(`Conversation ${conversationId} not found`);
    }

    // Add user message
    const userMsg = await ConversationService.addMessage(
      "user", 
      userMessage, 
      conversationId
    );

    // Get conversation history for context
    const messages = await ConversationService.getConversationMessages(conversationId);
    
    // Build context from previous messages
    const context = this.buildContextFromMessages(messages);
    
    // Get system prompt
    const systemPrompt = await PromptService.getSystemPrompt();
    
    // Generate response with context
    const fullPrompt = `${systemPrompt}\n\nConversation history:\n${context}\n\nNew user message: ${userMessage}`;
    const llmResponse = await this.llmService.generate(systemPrompt, fullPrompt);

    // Validate and save response
    if (!this.isValidResponse(llmResponse)) {
      throw new Error("Invalid response from LLM");
    }

    const responseMessage = llmResponse.reply[0];
    const savedMessage = await ConversationService.addMessage(
      responseMessage.character, 
      responseMessage.text, 
      conversationId
    );

    return {
      response: llmResponse,
      conversationId,
      messageId: savedMessage.id,
    };
  }

  private isValidResponse(response: any): response is ChatResponse {
    return (
      response &&
      typeof response === 'object' &&
      Array.isArray(response.reply) &&
      response.reply.length > 0 &&
      Array.isArray(response.skills) &&
      typeof response.theme === 'string'
    );
  }

  private buildContextFromMessages(messages: any[]): string {
    return messages
      .slice(-10) // Only use last 10 messages for context
      .map(msg => `${msg.character}: ${msg.text}`)
      .join('\n');
  }
}
